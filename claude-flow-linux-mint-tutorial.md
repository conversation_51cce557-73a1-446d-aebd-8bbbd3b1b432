# Claude-<PERSON> Complete Tutorial for Linux Mint Users

## 🎯 What is Claude<PERSON><PERSON>?

Claude-Flow v2.0.0 Alpha is an enterprise-grade AI orchestration platform that enables **hive-mind swarm intelligence** for software development. Think of it as having a team of 64 specialized AI developers working together on your projects, coordinated by a "Queen" AI that manages the entire swarm.

### Key Capabilities:
- **64 Specialized AI Agents**: From coders to testers to project managers
- **Hive-Mind Intelligence**: Agents coordinate and share knowledge automatically
- **87 MCP Tools**: Advanced tools for memory, automation, and orchestration
- **Real Results**: Users have built complete applications in days, not weeks
- **SPARC Methodology**: Specification → Pseudocode → Architecture → Refinement → Completion

## 📋 Prerequisites and System Requirements

### Minimum Requirements:
- **Operating System**: Linux Mint 20+ (or any Ubuntu-based distribution)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space for installation and projects
- **Internet**: Stable connection for package downloads and AI API calls

### Required Software:
- **Node.js**: Version 18 or higher
- **npm**: Version 9 or higher (comes with Node.js)
- **Terminal**: Any terminal application (built-in Terminal works fine)

## 🔧 Step 1: Check Your Current System

Open Terminal (Ctrl+Alt+T) and run these commands to check your system:

```bash
# Check if Node.js is installed and version
node --version

# Check npm version
npm --version

# Check your Linux Mint version
cat /etc/os-release | grep VERSION
```

**Expected Output:**
- Node.js: v18.0.0 or higher
- npm: 9.0.0 or higher
- Linux Mint: 20.0 or higher

## 🚀 Step 2: Install Node.js (if needed)

If Node.js is not installed or is below version 18, install it:

### Method 1: Using NodeSource Repository (Recommended)
```bash
# Download and run the NodeSource setup script
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -

# Install Node.js
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### Method 2: Using Snap (Alternative)
```bash
# Install Node.js via Snap
sudo snap install node --classic

# Verify installation
node --version
npm --version
```

## 🎯 Step 3: Install Claude Code

Claude-Flow requires Claude Code as its foundation. Install it globally:

```bash
# Install Claude Code globally
npm install -g @anthropic-ai/claude-code

# Verify installation
claude --version
```

**Troubleshooting**: If you get permission errors, use:
```bash
sudo npm install -g @anthropic-ai/claude-code
```

## 🌊 Step 4: Install and Initialize Claude-Flow

Now install Claude-Flow using NPX (no global installation needed):

```bash
# Initialize Claude-Flow (this downloads and sets up everything)
npx claude-flow@alpha init --force

# Alternative: Install globally if you prefer
npm install -g claude-flow@alpha
```

**What this does:**
- Downloads the latest Claude-Flow alpha version
- Sets up the SQLite memory system
- Creates configuration files
- Initializes the agent swarm system

## ⚙️ Step 5: Initial Setup and Configuration

### Configure Claude Code Authentication
```bash
# Start Claude Code to set up authentication
claude --dangerously-skip-permissions
```

This will:
1. Open a browser window for Anthropic authentication
2. Ask you to log in to your Claude account
3. Generate an API key for local use

**Note**: If the browser doesn't open automatically, copy the URL from the terminal and paste it into your browser.

### Verify Claude-Flow Installation
```bash
# Check Claude-Flow version
npx claude-flow@alpha --version

# Check system status
npx claude-flow@alpha status

# Test basic functionality
npx claude-flow@alpha agent list
```

## ✅ Step 6: Verification Tests

Let's verify everything is working correctly:

### Test 1: Basic Agent Creation
```bash
# Create a simple research agent
npx claude-flow@alpha agent spawn researcher --name "Test Agent"

# List all agents to see if it was created
npx claude-flow@alpha agent list
```

### Test 2: Simple Task Execution
```bash
# Create a basic task
npx claude-flow@alpha task create research "What is the current version of Linux Mint?" --assign-to researcher

# Check task status
npx claude-flow@alpha task list
```

### Test 3: Memory System Check
```bash
# Check memory system status
npx claude-flow@alpha memory stats

# Query memory contents
npx claude-flow@alpha memory query --limit 5
```

## 🎉 Success Indicators

You'll know everything is working when you see:

✅ **Node.js and npm** versions 18+ and 9+ respectively  
✅ **Claude Code** authentication successful  
✅ **Claude-Flow** version displays without errors  
✅ **Agent creation** works without errors  
✅ **Memory system** shows statistics  
✅ **Task creation** completes successfully  

## 🔧 Common Installation Issues and Solutions

### Issue 1: "Command not found: node"
**Solution:**
```bash
# Add Node.js to your PATH
echo 'export PATH="/usr/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### Issue 2: "Permission denied" during npm install
**Solution:**
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
# Or use sudo for global installs
sudo npm install -g @anthropic-ai/claude-code
```

### Issue 3: "SQLite binding failed"
**Solution:**
```bash
# Install build tools
sudo apt update
sudo apt install build-essential python3-dev

# Reinstall with force
npx claude-flow@alpha init --force
```

### Issue 4: "Claude authentication failed"
**Solution:**
1. Make sure you have a Claude Pro or Claude Team account
2. Try using a different browser
3. Clear browser cache and cookies
4. Run: `claude --dangerously-skip-permissions` again

### Issue 5: "Network timeout" during installation
**Solution:**
```bash
# Increase npm timeout
npm config set timeout 60000

# Try installation again
npx claude-flow@alpha init --force
```

## 📁 File Structure After Installation

After successful installation, you'll have:

```
~/.claude/                    # Claude Code configuration
├── config.json              # Authentication settings
└── cache/                   # Cached data

./.swarm/                    # Claude-Flow data (in project directory)
├── memory.db               # SQLite database
├── agents/                 # Agent configurations
└── logs/                   # System logs

./CLAUDE.md                 # Project configuration (if created)
```

## 🎯 Next Steps

Congratulations! You now have Claude-Flow installed and ready. Here's what to do next:

1. **Read the Practical Examples** (see claude-flow-examples.md)
2. **Try building your first application** with the swarm
3. **Explore the 64 different agent types** available
4. **Learn about the SPARC methodology** for structured development

## 📞 Getting Help

If you encounter issues:

1. **Check the logs**: `npx claude-flow@alpha logs --tail 50`
2. **Validate configuration**: `npx claude-flow@alpha config validate`
3. **System diagnostics**: `npx claude-flow@alpha doctor`
4. **Community support**: GitHub Issues at https://github.com/ruvnet/claude-flow
5. **Documentation**: Full wiki at https://github.com/ruvnet/claude-flow/wiki

## 🔄 Updating Claude-Flow

To update to the latest version:

```bash
# Update to latest alpha version
npx claude-flow@alpha update

# Or reinstall completely
npx claude-flow@alpha init --force
```

---

**🎉 You're now ready to harness the power of AI swarm intelligence for your development projects!**

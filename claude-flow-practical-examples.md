# Claude-Flow Practical Examples: Real-World Applications

## 🎯 Overview

This guide provides complete, working examples that demonstrate <PERSON><PERSON><PERSON>'s capabilities for real-world application development. Each example includes exact commands, expected outputs, and step-by-step explanations.

## 📋 Prerequisites

Before starting these examples, ensure you have:
- ✅ Claude-<PERSON> installed and configured (see claude-flow-linux-mint-tutorial.md)
- ✅ Claude Code authenticated
- ✅ Terminal access
- ✅ Basic understanding of command line

## 🏗️ Example 1: Building a Complete Backend API with SQLite Database

This example demonstrates building a full REST API for a task management system with database operations.

### Step 1: Initialize the Project
```bash
# Create project directory
mkdir task-manager-api
cd task-manager-api

# Initialize <PERSON><PERSON><PERSON> for this project
npx claude-flow@alpha init --sparc

# Create project specification
cat > project-spec.md << 'EOF'
# Task Manager API Specification

## Requirements:
- REST API with CRUD operations for tasks
- SQLite database with proper schema
- Authentication middleware
- Input validation
- Error handling
- API documentation

## Endpoints:
- GET /api/tasks - List all tasks
- POST /api/tasks - Create new task
- GET /api/tasks/:id - Get specific task
- PUT /api/tasks/:id - Update task
- DELETE /api/tasks/:id - Delete task
- POST /api/auth/login - User authentication

## Database Schema:
- Users table (id, username, password_hash, created_at)
- Tasks table (id, title, description, status, user_id, created_at, updated_at)
EOF
```

### Step 2: Deploy the Swarm
```bash
# Deploy a 5-agent swarm to build the API
npx claude-flow@alpha swarm "Build a complete REST API for task management based on project-spec.md. Use Node.js, Express, SQLite, and include proper authentication, validation, and documentation." --agents 5
```

**Expected Output:**
```
🧠 Claude-Flow Swarm Orchestrator v2.0.0-alpha
✅ Spawning 5 specialized agents...
✅ Agent 1: Backend Architect (system-architect)
✅ Agent 2: Database Designer (database-specialist)  
✅ Agent 3: API Developer (backend-dev)
✅ Agent 4: Security Engineer (security-specialist)
✅ Agent 5: Test Engineer (tester)

🔄 Agents coordinating through hive-mind...
📊 Progress: Database schema created
📊 Progress: Express server configured
📊 Progress: Authentication middleware implemented
📊 Progress: CRUD endpoints developed
📊 Progress: Tests written and passing
✅ Task completed successfully!
```

### Step 3: Review Generated Code
```bash
# List generated files
ls -la

# Check the main server file
cat server.js

# Review database schema
cat database/schema.sql

# Check API documentation
cat docs/api.md
```

### Step 4: Test the API
```bash
# Install dependencies
npm install

# Start the server
npm start

# In another terminal, test the endpoints
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'

# Create a task
curl -X POST http://localhost:3000/api/tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"title": "Test Task", "description": "This is a test task"}'

# List all tasks
curl http://localhost:3000/api/tasks \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🎨 Example 2: Overhauling an Existing Application's User Interface

This example shows how to modernize a legacy web application's frontend.

### Step 1: Prepare the Legacy Application
```bash
# Create a sample legacy app
mkdir legacy-app-modernization
cd legacy-app-modernization

# Create a basic legacy HTML structure
cat > legacy-app.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Legacy App</title>
    <style>
        body { font-family: Arial; background: #f0f0f0; }
        .container { width: 800px; margin: 0 auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Employee Management System</h1>
        <table>
            <tr><th>ID</th><th>Name</th><th>Department</th><th>Actions</th></tr>
            <tr><td>1</td><td>John Doe</td><td>Engineering</td><td><button>Edit</button></td></tr>
        </table>
        <form>
            <input type="text" placeholder="Name">
            <input type="text" placeholder="Department">
            <button type="submit">Add Employee</button>
        </form>
    </div>
</body>
</html>
EOF

# Create modernization requirements
cat > modernization-spec.md << 'EOF'
# UI Modernization Requirements

## Current State:
- Basic HTML table interface
- Inline CSS styling
- No responsive design
- No modern interactions

## Target State:
- Modern React-based interface
- Responsive design with Tailwind CSS
- Interactive components (search, filters, pagination)
- Dark/light mode toggle
- Mobile-friendly design
- Smooth animations and transitions
- Accessibility compliance (WCAG 2.1)

## Features to Add:
- Real-time search functionality
- Sortable columns
- Bulk operations
- Export functionality
- User preferences persistence
EOF
```

### Step 2: Deploy UI Modernization Swarm
```bash
# Deploy specialized UI/UX swarm
npx claude-flow@alpha swarm "Modernize the legacy employee management interface based on modernization-spec.md. Create a modern React application with Tailwind CSS, responsive design, and enhanced user experience." --agents 6
```

### Step 3: Review and Test the Modern Interface
```bash
# Check generated React components
ls -la src/components/

# Review the main App component
cat src/App.jsx

# Check responsive design utilities
cat src/styles/responsive.css

# Install and run the modern app
npm install
npm run dev

# Open browser to http://localhost:5173
```

## 🐛 Example 3: Debugging Existing Code with Claude-Flow

This example demonstrates using Claude-Flow to identify and fix bugs in existing code.

### Step 1: Create Buggy Code Sample
```bash
mkdir debug-example
cd debug-example

# Create a buggy Python script
cat > buggy_calculator.py << 'EOF'
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def divide(self, a, b):
        result = a / b  # Bug: No zero division check
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def get_average(self, numbers):
        total = sum(numbers)
        return total / len(numbers)  # Bug: No empty list check
    
    def factorial(self, n):
        if n == 0:
            return 1
        return n * self.factorial(n - 1)  # Bug: No negative number check

# Test the calculator
calc = Calculator()
print(calc.add(5, 3))
print(calc.divide(10, 0))  # This will crash
print(calc.get_average([]))  # This will crash
print(calc.factorial(-5))  # This will cause infinite recursion
EOF

# Create debugging requirements
cat > debug-requirements.md << 'EOF'
# Debug Requirements

## Issues to Find and Fix:
1. Division by zero errors
2. Empty list handling
3. Negative number validation
4. Error handling and logging
5. Input validation
6. Performance optimization
7. Code documentation

## Expected Improvements:
- Comprehensive error handling
- Input validation
- Logging system
- Unit tests
- Performance optimizations
- Clear documentation
EOF
```

### Step 2: Deploy Debugging Swarm
```bash
# Deploy debugging specialists
npx claude-flow@alpha swarm "Debug and fix the buggy_calculator.py file according to debug-requirements.md. Add proper error handling, validation, tests, and documentation." --agents 4
```

### Step 3: Review Fixed Code
```bash
# Check the fixed calculator
cat calculator_fixed.py

# Review the test suite
cat test_calculator.py

# Check documentation
cat README.md

# Run tests to verify fixes
python -m pytest test_calculator.py -v
```

## 🔄 Example 4: Refactoring Legacy Code for Better Maintainability

This example shows how to refactor monolithic code into clean, maintainable modules.

### Step 1: Create Legacy Monolithic Code
```bash
mkdir refactoring-example
cd refactoring-example

# Create a monolithic e-commerce script
cat > legacy_ecommerce.py << 'EOF'
# Legacy monolithic e-commerce system
import sqlite3
import hashlib
import datetime

class ECommerceSystem:
    def __init__(self):
        self.conn = sqlite3.connect('ecommerce.db')
        self.setup_database()
    
    def setup_database(self):
        # All database setup in one method
        cursor = self.conn.cursor()
        cursor.execute('''CREATE TABLE IF NOT EXISTS users 
                         (id INTEGER PRIMARY KEY, username TEXT, password TEXT, email TEXT)''')
        cursor.execute('''CREATE TABLE IF NOT EXISTS products 
                         (id INTEGER PRIMARY KEY, name TEXT, price REAL, stock INTEGER)''')
        cursor.execute('''CREATE TABLE IF NOT EXISTS orders 
                         (id INTEGER PRIMARY KEY, user_id INTEGER, product_id INTEGER, quantity INTEGER, total REAL)''')
        self.conn.commit()
    
    def register_user(self, username, password, email):
        # User registration logic mixed with database operations
        hashed_password = hashlib.md5(password.encode()).hexdigest()
        cursor = self.conn.cursor()
        cursor.execute("INSERT INTO users (username, password, email) VALUES (?, ?, ?)", 
                      (username, hashed_password, email))
        self.conn.commit()
        return cursor.lastrowid
    
    def add_product(self, name, price, stock):
        # Product management mixed with database operations
        cursor = self.conn.cursor()
        cursor.execute("INSERT INTO products (name, price, stock) VALUES (?, ?, ?)", 
                      (name, price, stock))
        self.conn.commit()
        return cursor.lastrowid
    
    def place_order(self, user_id, product_id, quantity):
        # Order processing with no separation of concerns
        cursor = self.conn.cursor()
        cursor.execute("SELECT price, stock FROM products WHERE id = ?", (product_id,))
        product = cursor.fetchone()
        
        if product and product[1] >= quantity:
            total = product[0] * quantity
            cursor.execute("INSERT INTO orders (user_id, product_id, quantity, total) VALUES (?, ?, ?, ?)",
                          (user_id, product_id, quantity, total))
            cursor.execute("UPDATE products SET stock = stock - ? WHERE id = ?", (quantity, product_id))
            self.conn.commit()
            return cursor.lastrowid
        return None

# Usage example
system = ECommerceSystem()
user_id = system.register_user("john_doe", "password123", "<EMAIL>")
product_id = system.add_product("Laptop", 999.99, 10)
order_id = system.place_order(user_id, product_id, 1)
EOF

# Create refactoring requirements
cat > refactoring-spec.md << 'EOF'
# Refactoring Requirements

## Current Issues:
- Monolithic class with mixed responsibilities
- No separation of concerns
- Database operations mixed with business logic
- No error handling
- No input validation
- Hard to test and maintain

## Target Architecture:
- Separate models for User, Product, Order
- Database abstraction layer
- Service layer for business logic
- Repository pattern for data access
- Proper error handling and validation
- Dependency injection
- Unit tests for all components
- Configuration management

## Design Patterns to Implement:
- Repository Pattern
- Service Layer Pattern
- Factory Pattern
- Dependency Injection
- Observer Pattern (for events)
EOF
```

### Step 2: Deploy Refactoring Swarm
```bash
# Deploy architecture and refactoring specialists
npx claude-flow@alpha swarm "Refactor the legacy_ecommerce.py monolithic code according to refactoring-spec.md. Implement clean architecture with proper separation of concerns, design patterns, and comprehensive testing." --agents 7
```

### Step 3: Review Refactored Architecture
```bash
# Check the new modular structure
tree .

# Review the models
cat models/user.py
cat models/product.py
cat models/order.py

# Check the repository layer
cat repositories/user_repository.py

# Review the service layer
cat services/order_service.py

# Check the main application
cat main.py

# Run tests
python -m pytest tests/ -v
```

## 🎯 Example 5: Building a Complete Full-Stack Application

This comprehensive example builds a complete blog application with frontend, backend, and database.

### Step 1: Define the Complete Application
```bash
mkdir fullstack-blog
cd fullstack-blog

# Create comprehensive specification
cat > fullstack-spec.md << 'EOF'
# Full-Stack Blog Application

## Architecture:
- Frontend: React with TypeScript and Tailwind CSS
- Backend: Node.js with Express and TypeScript
- Database: PostgreSQL with Prisma ORM
- Authentication: JWT with refresh tokens
- File Upload: Cloudinary integration
- Real-time: WebSocket for comments

## Features:
### User Management:
- User registration and login
- Profile management with avatar upload
- Password reset functionality

### Blog Management:
- Create, edit, delete blog posts
- Rich text editor with image upload
- Categories and tags
- Draft and published states

### Interaction:
- Comments system with real-time updates
- Like/unlike posts
- Follow/unfollow users
- Search functionality

### Admin Panel:
- User management
- Content moderation
- Analytics dashboard

## Technical Requirements:
- Responsive design
- SEO optimization
- Performance optimization
- Security best practices
- Comprehensive testing
- CI/CD pipeline
- Docker deployment
EOF
```

### Step 2: Deploy Full-Stack Development Swarm
```bash
# Deploy a large swarm for full-stack development
npx claude-flow@alpha swarm "Build a complete full-stack blog application based on fullstack-spec.md. Include frontend, backend, database, authentication, real-time features, and deployment configuration." --agents 12
```

### Step 3: Review and Deploy the Application
```bash
# Check the project structure
tree -L 3

# Review package configurations
cat frontend/package.json
cat backend/package.json

# Check Docker configuration
cat docker-compose.yml

# Start the development environment
docker-compose up -d

# Install dependencies and start development servers
cd frontend && npm install && npm run dev &
cd ../backend && npm install && npm run dev &

# Open the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# Database Admin: http://localhost:5432
```

## 📊 Performance Metrics and Results

Based on real-world usage, Claude-Flow typically delivers:

### Development Speed:
- **Traditional Development**: 2-4 weeks for a full-stack application
- **With Claude-Flow**: 2-4 days for the same application
- **Speed Improvement**: 5-10x faster development

### Code Quality:
- **Test Coverage**: 85%+ automatically generated
- **Documentation**: Comprehensive docs included
- **Best Practices**: Industry standards automatically applied
- **Security**: Security patterns implemented by default

### Real User Results:
- **Adrian Cockcroft**: Built complete IoT system in 2 days (150,000+ lines)
- **SWE-Bench Score**: 84.8% problem-solving success rate
- **Token Efficiency**: 32.3% reduction in API calls
- **Performance**: 2.8-4.4x speed improvement over traditional methods

## 🎯 Next Steps

After completing these examples, you're ready to:

1. **Build Your Own Projects**: Apply these patterns to your specific needs
2. **Explore Advanced Features**: Learn about the 64 agent types and specialized workflows
3. **Integrate with CI/CD**: Set up automated development pipelines
4. **Scale Your Team**: Use Claude-Flow for team collaboration and knowledge sharing

## 📞 Support and Resources

- **Documentation**: https://github.com/ruvnet/claude-flow/wiki
- **Examples Repository**: https://github.com/ruvnet/claude-flow/tree/main/examples
- **Community**: GitHub Discussions and Issues
- **Updates**: Follow @ruvnet for latest developments

---

**🚀 You now have practical experience with Claude-Flow's capabilities. Start building amazing applications with AI-powered development!**

# Claude-Flow Troubleshooting Guide: Foolproof Solutions for Linux Mint

## 🎯 Quick Diagnostic Commands

Before diving into specific issues, run these diagnostic commands to understand your system state:

```bash
# System Information
echo "=== SYSTEM INFO ==="
cat /etc/os-release | grep -E "(NAME|VERSION)"
uname -a

# Node.js and npm Status
echo "=== NODE.JS STATUS ==="
which node && node --version || echo "Node.js not found"
which npm && npm --version || echo "npm not found"

# Claude Code Status
echo "=== CLAUDE CODE STATUS ==="
which claude && claude --version || echo "Claude Code not installed"

# Claude-Flow Status
echo "=== CLAUDE-FLOW STATUS ==="
npx claude-flow@alpha --version 2>/dev/null || echo "Claude-Flow not accessible"

# Network Connectivity
echo "=== NETWORK STATUS ==="
ping -c 1 google.com >/dev/null 2>&1 && echo "Internet: OK" || echo "Internet: FAILED"
ping -c 1 registry.npmjs.org >/dev/null 2>&1 && echo "npm registry: OK" || echo "npm registry: FAILED"
```

## 🚨 Common Installation Issues

### Issue 1: "Command 'node' not found"

**Symptoms:**
```bash
$ node --version
bash: node: command not found
```

**Solutions:**

**Option A: Install via NodeSource (Recommended)**
```bash
# Download and install NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -

# Install Node.js
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

**Option B: Install via Snap**
```bash
# Install Node.js via Snap
sudo snap install node --classic

# Add to PATH if needed
echo 'export PATH="/snap/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Verify installation
node --version
```

**Option C: Manual Installation**
```bash
# Download latest LTS version
wget https://nodejs.org/dist/v20.10.0/node-v20.10.0-linux-x64.tar.xz

# Extract and install
tar -xf node-v20.10.0-linux-x64.tar.xz
sudo mv node-v20.10.0-linux-x64 /opt/nodejs
sudo ln -s /opt/nodejs/bin/node /usr/local/bin/node
sudo ln -s /opt/nodejs/bin/npm /usr/local/bin/npm

# Verify installation
node --version
```

### Issue 2: "Permission denied" during npm install

**Symptoms:**
```bash
$ npm install -g @anthropic-ai/claude-code
npm ERR! Error: EACCES: permission denied, mkdir '/usr/local/lib/node_modules'
```

**Solutions:**

**Option A: Fix npm permissions (Recommended)**
```bash
# Create npm global directory in home folder
mkdir ~/.npm-global

# Configure npm to use new directory
npm config set prefix '~/.npm-global'

# Add to PATH
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# Now install without sudo
npm install -g @anthropic-ai/claude-code
```

**Option B: Use sudo (Quick fix)**
```bash
# Install with sudo
sudo npm install -g @anthropic-ai/claude-code

# Fix ownership of npm cache
sudo chown -R $(whoami) ~/.npm
```

### Issue 3: "Claude authentication failed"

**Symptoms:**
```bash
$ claude --dangerously-skip-permissions
Error: Authentication failed. Please check your API key.
```

**Solutions:**

**Step 1: Verify Claude Account**
- Ensure you have a Claude Pro or Claude Team subscription
- Visit https://claude.ai and verify you can log in

**Step 2: Clear existing authentication**
```bash
# Remove existing Claude configuration
rm -rf ~/.claude

# Clear npm cache
npm cache clean --force

# Reinstall Claude Code
npm uninstall -g @anthropic-ai/claude-code
npm install -g @anthropic-ai/claude-code
```

**Step 3: Manual authentication**
```bash
# Start Claude with verbose output
claude --dangerously-skip-permissions --verbose

# If browser doesn't open, copy the URL manually
# Look for output like: "Open this URL in your browser: https://..."
```

**Step 4: Alternative browser setup**
```bash
# If default browser fails, try with specific browser
BROWSER=firefox claude --dangerously-skip-permissions
# or
BROWSER=google-chrome claude --dangerously-skip-permissions
```

### Issue 4: "SQLite binding failed" or "better-sqlite3 error"

**Symptoms:**
```bash
$ npx claude-flow@alpha init
Error: Could not locate the bindings file
```

**Solutions:**

**Option A: Install build tools**
```bash
# Install required build tools
sudo apt update
sudo apt install -y build-essential python3-dev python3-pip

# Install node-gyp globally
npm install -g node-gyp

# Clear npm cache and retry
npm cache clean --force
npx claude-flow@alpha init --force
```

**Option B: Use in-memory fallback**
```bash
# Claude-Flow will automatically fall back to in-memory storage
npx claude-flow@alpha init --memory-mode in-memory
```

**Option C: Install SQLite development libraries**
```bash
# Install SQLite development packages
sudo apt install -y libsqlite3-dev

# Retry installation
npx claude-flow@alpha init --force
```

### Issue 5: "Network timeout" or "ENOTFOUND" errors

**Symptoms:**
```bash
$ npx claude-flow@alpha init
npm ERR! network timeout at: https://registry.npmjs.org/claude-flow
```

**Solutions:**

**Option A: Configure npm timeout and registry**
```bash
# Increase timeout
npm config set timeout 60000

# Use alternative registry if needed
npm config set registry https://registry.npmjs.org/

# Clear cache and retry
npm cache clean --force
npx claude-flow@alpha init --force
```

**Option B: Use proxy if behind corporate firewall**
```bash
# Configure proxy (replace with your proxy details)
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# Retry installation
npx claude-flow@alpha init
```

**Option C: Download and install manually**
```bash
# Download package manually
wget https://registry.npmjs.org/claude-flow/-/claude-flow-2.0.0-alpha.73.tgz

# Install from local file
npm install -g claude-flow-2.0.0-alpha.73.tgz
```

## 🔧 Runtime Issues

### Issue 6: "Agent spawn failed" or "No agents available"

**Symptoms:**
```bash
$ npx claude-flow@alpha agent spawn researcher
Error: Failed to spawn agent - no available terminals
```

**Solutions:**

**Check system resources:**
```bash
# Check available memory
free -h

# Check CPU usage
top -n 1 | head -20

# Check disk space
df -h
```

**Restart Claude-Flow:**
```bash
# Kill any existing processes
pkill -f claude-flow

# Clear temporary files
rm -rf /tmp/claude-flow-*

# Restart with verbose logging
npx claude-flow@alpha init --force --verbose
```

### Issue 7: "Memory system initialization failed"

**Symptoms:**
```bash
$ npx claude-flow@alpha status
Error: Memory system not initialized
```

**Solutions:**

**Option A: Reset memory system**
```bash
# Remove existing memory database
rm -rf .swarm/

# Reinitialize
npx claude-flow@alpha init --force
```

**Option B: Use alternative memory backend**
```bash
# Initialize with in-memory storage
npx claude-flow@alpha init --memory-backend memory

# Or use file-based storage
npx claude-flow@alpha init --memory-backend file
```

### Issue 8: "Task execution timeout"

**Symptoms:**
```bash
$ npx claude-flow@alpha task create research "Complex research task"
Error: Task execution timeout after 300 seconds
```

**Solutions:**

**Increase timeout:**
```bash
# Set longer timeout (in seconds)
npx claude-flow@alpha config set task.timeout 1800

# Or specify timeout for specific task
npx claude-flow@alpha task create research "Complex task" --timeout 1800
```

**Break down complex tasks:**
```bash
# Instead of one complex task, create multiple simpler tasks
npx claude-flow@alpha task create research "Research part 1: Background"
npx claude-flow@alpha task create research "Research part 2: Current state"
npx claude-flow@alpha task create research "Research part 3: Analysis"
```

## 🐛 Debugging and Diagnostics

### Enable Verbose Logging
```bash
# Run any command with verbose output
npx claude-flow@alpha --verbose [command]

# Enable debug mode
export DEBUG=claude-flow:*
npx claude-flow@alpha [command]

# Check system logs
npx claude-flow@alpha logs --tail 50
```

### System Health Check
```bash
# Comprehensive system check
npx claude-flow@alpha doctor

# Check configuration
npx claude-flow@alpha config validate

# Test basic functionality
npx claude-flow@alpha test --basic
```

### Memory and Performance Monitoring
```bash
# Check memory usage
npx claude-flow@alpha memory stats

# Monitor system performance
npx claude-flow@alpha monitor --duration 60

# Check agent status
npx claude-flow@alpha agent list --detailed
```

## 📞 Getting Additional Help

### Self-Help Resources
1. **Check logs first**: `npx claude-flow@alpha logs --tail 100`
2. **Run diagnostics**: `npx claude-flow@alpha doctor`
3. **Validate config**: `npx claude-flow@alpha config validate`
4. **Test connectivity**: `npx claude-flow@alpha test --network`

### Community Support
- **GitHub Issues**: https://github.com/ruvnet/claude-flow/issues
- **GitHub Discussions**: https://github.com/ruvnet/claude-flow/discussions
- **Documentation**: https://github.com/ruvnet/claude-flow/wiki
- **Examples**: https://github.com/ruvnet/claude-flow/tree/main/examples

### Reporting Issues
When reporting issues, include:

```bash
# Generate diagnostic report
npx claude-flow@alpha diagnostic-report > claude-flow-diagnostic.txt

# Include this information:
# 1. Your Linux Mint version
# 2. Node.js and npm versions
# 3. Complete error messages
# 4. Steps to reproduce
# 5. Expected vs actual behavior
```

## 🔄 Maintenance and Updates

### Regular Maintenance
```bash
# Update to latest version
npx claude-flow@alpha update

# Clean cache and temporary files
npx claude-flow@alpha clean

# Optimize memory database
npx claude-flow@alpha memory optimize

# Backup important data
npx claude-flow@alpha backup create
```

### Performance Optimization
```bash
# Configure for better performance
npx claude-flow@alpha config set performance.mode optimized

# Adjust memory limits
npx claude-flow@alpha config set memory.limit 2048

# Enable caching
npx claude-flow@alpha config set cache.enabled true
```

## ✅ Success Verification Checklist

After resolving any issues, verify everything works:

```bash
# 1. Basic installation check
node --version && npm --version

# 2. Claude Code authentication
claude --version

# 3. Claude-Flow accessibility
npx claude-flow@alpha --version

# 4. System initialization
npx claude-flow@alpha status

# 5. Agent creation test
npx claude-flow@alpha agent spawn researcher --name "Test Agent"

# 6. Task execution test
npx claude-flow@alpha task create research "Test task" --assign-to researcher

# 7. Memory system check
npx claude-flow@alpha memory stats

# 8. Cleanup test agents
npx claude-flow@alpha agent terminate researcher
```

If all commands complete successfully, your Claude-Flow installation is working correctly!

---

**🎉 With this troubleshooting guide, you should be able to resolve any issues and get Claude-Flow running smoothly on Linux Mint!**
